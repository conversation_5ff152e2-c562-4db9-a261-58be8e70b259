/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #334155;
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 页面头部 */
.header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    padding: 6px 16px;
    box-shadow: 0 4px 20px rgba(30, 41, 59, 0.15);
    position: relative;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 390px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: url('images/图标2.png') no-repeat center center;
    background-size: contain;
    border-radius: 8px;
    position: relative;
}

.logo-text {
    font-size: 16px;
    font-weight: 600;
}

.page-title {
    font-size: 14px;
    font-weight: 600;
}

/* 地图容器 */
.map-container {
    padding: 16px;
    max-width: 390px;
    margin: 0 auto;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.map-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(51, 65, 85, 0.08);
    overflow: hidden;
    position: relative;
    height: 550px;
    margin-bottom: 8px;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.map-background {
    width: 100%;
    height: 100%;
    position: relative;
}

.beijing-map-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 8px;
    transition: opacity 0.3s ease;
}

.beijing-map-image:not([src]) {
    opacity: 0;
}

/* 项目标记 */
.project-markers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.marker {
    position: absolute;
    width: 12px;
    height: 12px;
    cursor: pointer;
    pointer-events: all;
    transform: translate(-50%, -50%);
    z-index: 10;
    opacity: 0;
    visibility: hidden;
}

.marker-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1.5px solid white;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.marker-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse 2s infinite;
}

/* 在施项目标记颜色 */
.marker.active-project .marker-icon {
    background-color: #8a0101;
}

.marker.active-project .marker-pulse {
    background-color: #8a0101;
}

/* 突出显示的标记点样式 */
.marker.highlighted {
    transition: all 0.3s ease;
}

.marker.highlighted .marker-icon {
    background-color: #dc2626;
    border: 3px solid #fef2f2;
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.6);
}

.marker.highlighted .marker-pulse {
    background-color: #dc2626;
    animation: highlightPulse 1s infinite;
}

@keyframes highlightPulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(2);
        opacity: 0.3;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

/* 标记点击效果 */
.marker:hover .marker-icon {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.marker:active .marker-icon {
    transform: scale(1.1);
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.3;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}



/* 项目列表 */
.project-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(51, 65, 85, 0.06);
    margin-bottom: 0px;
    margin-top: auto;
    border: 1px solid rgba(226, 232, 240, 0.4);
    max-height: 150px;
    display: flex;
    flex-direction: column;
}

.project-list-header {
    padding: 4px 16px;
    font-size: 11px;
    font-weight: 600;
    color: #334155;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: #f8fafc;
    border-radius: 12px 12px 0 0;
}

.project-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.project-item {
    display: flex;
    align-items: center;
    padding: 3px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 3px solid transparent;
}

.project-item:hover {
    background-color: #f8fafc;
    border-left-color: #8a0101;
}

.project-list .project-number {
    font-size: 12px;
    font-weight: 600;
    color: #8a0101;
    min-width: 22px;
    margin-right: 8px;
}

.project-list .project-name {
    font-size: 12px;
    color: #475569;
    line-height: 1.1;
    flex: 1;
    font-weight: 400;
}

.stat-item {
    text-align: center;
    flex: 1;
    position: relative;
}

.stat-item.clickable {
    cursor: pointer;
    border-radius: 8px;
    padding: 8px 4px;
    margin: -8px -4px;
    transition: all 0.2s ease;
}

.stat-item.clickable:hover {
    background-color: #f1f5f9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(51, 65, 85, 0.1);
}

.stat-item.clickable:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(51, 65, 85, 0.1);
}

.stat-item.active {
    box-shadow: 0 2px 12px rgba(51, 65, 85, 0.15);
}

/* 不同状态的激活样式 */
.stat-item[data-filter="all"].active {
    background-color: #f1f5f9;
}

.stat-item[data-filter="in-progress"].active {
    background-color: #eff6ff;
}

.stat-item[data-filter="completed"].active {
    background-color: #ecfdf5;
}

.stat-item[data-filter="planning"].active {
    background-color: #fffbeb;
}

.stat-item.active .stat-number {
    transform: scale(1.05);
}

.stat-item.active .stat-label {
    font-weight: 600;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
    transition: color 0.2s ease;
}

/* 不同状态的数字颜色 */
.stat-item[data-filter="all"] .stat-number {
    color: #64748b;
}

.stat-item[data-filter="in-progress"] .stat-number {
    color: #3b82f6;
}

.stat-item[data-filter="completed"] .stat-number {
    color: #10b981;
}

.stat-item[data-filter="planning"] .stat-number {
    color: #f59e0b;
}

.stat-label {
    font-size: 12px;
    color: #666;
    transition: all 0.2s ease;
}

/* 弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.project-modal {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: white;
    border-radius: 16px 16px 0 0;
    max-height: 70vh;
    overflow-y: auto;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.modal-overlay.active .project-modal {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.modal-header .project-name {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: #f5f5f5;
    color: #666;
}

.modal-content {
    padding: 0 20px 20px;
}

.project-image {
    margin-bottom: 20px;
}

.image-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #d9d9d9;
    position: relative;
    overflow: hidden;
}

.image-icon {
    font-size: 48px;
    opacity: 0.5;
}

/* 视频控制按钮 */
.video-controls {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);
    opacity: 1;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.image-placeholder:hover .video-controls {
    background: rgba(0, 0, 0, 0.3);
}

.play-pause-btn {
    width: 70px;
    height: 70px;
    background: rgba(24, 144, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.play-pause-btn:hover {
    background: rgba(24, 144, 255, 1);
    transform: scale(1.1);
    border-color: white;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.play-pause-btn:active {
    transform: scale(0.95);
}

.play-icon, .pause-icon {
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.play-icon {
    margin-left: 4px; /* 调整播放图标位置，使其视觉居中 */
}

/* 添加脉冲动画效果 */
@keyframes pulse-video {
    0% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(24, 144, 255, 0.7);
    }
    70% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 10px rgba(24, 144, 255, 0);
    }
    100% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(24, 144, 255, 0);
    }
}

.play-pause-btn {
    animation: pulse-video 2s infinite;
}

.play-pause-btn:hover {
    animation: none;
}

.project-info {
    padding-top: 12px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    line-height: 1.5;
}

.info-label {
    font-weight: 500;
    color: #666;
    min-width: 80px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
    flex: 1;
}

.info-value.investment {
    font-size: 18px;
    font-weight: 600;
    color: #1890FF;
}

.phone-link {
    color: #1890FF;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 4px 8px;
    margin: -4px -8px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.phone-link::before {
    content: '📞';
    font-size: 14px;
}

.phone-link:hover {
    background-color: #e6f7ff;
    color: #096dd9;
    text-decoration: underline;
}

.phone-link:active {
    background-color: #bae7ff;
    transform: scale(0.98);
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.completed {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-badge.in-progress {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.status-badge.planning {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

/* 响应式设计 */
@media (max-width: 390px) {
    .map-container {
        padding: 12px;
    }

    .header {
        padding: 10px 12px;
    }

    .page-title {
        font-size: 16px;
    }

    .logo-text {
        font-size: 14px;
    }

    .legend {
        top: 12px;
        right: 12px;
        padding: 10px;
    }

    .legend-title {
        font-size: 11px;
        margin-bottom: 6px;
    }

    .legend-items {
        gap: 5px;
    }

    .legend-label {
        font-size: 10px;
    }
}

/* 页脚样式 */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    color: white;
    margin-top: 0px;
    flex-shrink: 0;
}

.footer-content {
    max-width: 390px;
    margin: 0 auto;
    padding: 4px 16px;
}

.footer-info {
    text-align: center;
}

.footer-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-bottom: 0px;
}

.footer-logo-icon {
    width: 48px;
    height: 48px;
    background: url('images/图标2.png') no-repeat center center;
    background-size: contain;
    border-radius: 10px;
    position: relative;
}

.footer-logo-text {
    font-size: 12px;
    font-weight: 600;
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
    .marker {
        width: 18px;
        height: 18px;
    }

    .marker-icon {
        width: 18px;
        height: 18px;
        border: 2px solid white;
    }

    .marker-pulse {
        width: 18px;
        height: 18px;
    }

    .close-btn {
        width: 44px;
        height: 44px;
        font-size: 28px;
    }
}
