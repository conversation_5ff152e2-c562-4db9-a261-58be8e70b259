// 项目数据
const projectsData = {
    1: {
        name: "朝阳区崔各庄乡来广营北路等地块二类居住及住宅混合公建用地项目",
        company: "国建",
        district: "朝阳区",
        manager: "钟远享",
        phone: "13901286002",
        image: "🏘️"
    },
    2: {
        name: "朝阳区平房乡黄杉木店亮马厂安置房项目（西区）一标段",
        company: "六建",
        district: "朝阳区",
        manager: "马忠勉",
        phone: "13466777150",
        image: "🏠"
    },
    3: {
        name: "朝阳区十八里店乡小武基村农民安置房项目",
        company: "三建",
        district: "朝阳区",
        manager: "李杰",
        phone: "13718938449",
        image: "🏘️"
    },
    4: {
        name: "温榆河生态治理工程",
        company: "城乡集团",
        district: "朝阳区",
        manager: "华广胜",
        phone: "13910059606",
        image: "🌊"
    },
    5: {
        name: "朝阳区大羊坊路（东三环路—东五环路）道路工程1#标段",
        company: "市政股份",
        district: "朝阳区",
        manager: "梁磊",
        phone: "13910821047",
        image: "🛣️"
    },
    6: {
        name: "国家自然博物馆新馆建设",
        company: "总承包部",
        district: "丰台区",
        manager: "陆春雨",
        phone: "13601072952",
        image: "🏛️"
    },
    7: {
        name: "丰台区青塔街道青塔村棚户区改造项目",
        company: "城乡集团",
        district: "丰台区",
        manager: "康凯",
        phone: "13910398755",
        image: "🏗️"
    },
    8: {
        name: "国家网络安全园二期项目",
        company: "博海",
        district: "海淀区",
        manager: "许涛",
        phone: "15801567655",
        image: "🏢"
    },
    9: {
        name: "树村8号地置换集体产业项目",
        company: "二部",
        district: "海淀区",
        manager: "于新华",
        phone: "13911086950",
        image: "🏭"
    },
    10: {
        name: "京密路3标",
        company: "市政集团",
        district: "顺义区",
        manager: "李鑫",
        phone: "13621363563",
        image: "🛣️"
    },
    11: {
        name: "北京城市副中心站综合交通枢纽工程04标段",
        company: "市政集团",
        district: "通州区",
        manager: "史永杰",
        phone: "18500729165",
        image: "🚄"
    },
    12: {
        name: "北京卫生职业学院新院区建设项目二标段实验实训区",
        company: "六建",
        district: "通州区",
        manager: "姚玉春",
        phone: "18811037791",
        image: "🏫"
    },
    13: {
        name: "大兴区黄村镇海子角、辛店村棚户区改造土地开发A区项目四标段",
        company: "机施",
        district: "大兴区",
        manager: "王幸男",
        phone: "18518767846",
        image: "🏗️"
    },
    14: {
        name: "大兴区黄村镇兴业南路5、11号棚改定向安置房项目",
        company: "建工路桥",
        district: "大兴区",
        manager: "胡永忠",
        phone: "13911252556",
        image: "🏠"
    },
    15: {
        name: "昌平区沙河镇西沙屯村、满井西队村棚户区",
        company: "一建",
        district: "昌平区",
        manager: "贾鹏",
        phone: "13811002654",
        image: "🏗️"
    },
    16: {
        name: "北京市门头沟区育园小学",
        company: "四建",
        district: "门头沟区",
        manager: "王伟",
        phone: "18689687998",
        image: "🏫"
    },
    17: {
        name: "北京京东方医院项目",
        company: "四建",
        district: "房山区",
        manager: "王兰鸽",
        phone: "13911034610",
        image: "🏥"
    },
    18: {
        name: "108新线高速公路6标",
        company: "市政股份",
        district: "房山区",
        manager: "毕新建",
        phone: "13306425898",
        image: "🛣️"
    },
    19: {
        name: "108新线高速公路4标",
        company: "市政集团",
        district: "房山区",
        manager: "王海涛",
        phone: "15801267868",
        image: "🛣️"
    },
    20: {
        name: "北京中医药大学良乡校区中医哲学社科研究中心(B6)项目",
        company: "总承包部",
        district: "房山区",
        manager: "胡海宽",
        phone: "18638782751",
        image: "🏫"
    }
};

// DOM元素
const modalOverlay = document.getElementById('modalOverlay');
const projectModal = document.getElementById('projectModal');
const closeBtn = document.getElementById('closeBtn');
const projectName = document.getElementById('projectName');
const projectCompany = document.getElementById('projectCompany');
const projectDistrict = document.getElementById('projectDistrict');
const projectManager = document.getElementById('projectManager');
const projectPhone = document.getElementById('projectPhone');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 使用 try-catch 包装初始化，避免潜在错误
    try {
        initializeMarkers();
        initializeModal();
        addTouchSupport();
        initializeProjectList();
    } catch (error) {
        console.error('初始化过程中发生错误:', error);
    }
});

// 初始化标记点
function initializeMarkers() {
    const markers = document.querySelectorAll('.marker');
    
    markers.forEach(marker => {
        marker.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const projectId = this.getAttribute('data-project');
            showProjectModal(projectId);
            
            // 添加点击反馈动画
            this.style.transform = 'translate(-50%, -50%) scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 150);
        });
        
        // 添加触摸反馈
        marker.addEventListener('touchstart', function() {
            this.style.transform = 'translate(-50%, -50%) scale(1.1)';
        });
        
        marker.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 100);
        });
    });
}

// 显示项目弹窗
function showProjectModal(projectId) {
    const project = projectsData[projectId];
    if (!project) return;

    // 填充项目信息
    projectName.textContent = project.name;
    projectCompany.textContent = project.company;
    projectDistrict.textContent = project.district;
    projectManager.textContent = project.manager;

    // 设置电话链接
    projectPhone.textContent = project.phone;
    projectPhone.href = `tel:${project.phone}`;

    // 显示弹窗
    modalOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // 添加显示动画
    setTimeout(() => {
        projectModal.style.transform = 'translateY(0)';
    }, 10);
}

// 隐藏项目弹窗
function hideProjectModal() {
    modalOverlay.classList.remove('active');
    document.body.style.overflow = '';
    projectModal.style.transform = 'translateY(100%)';

    // 重置视频控制状态
    videoControlsInitialized = false;
    isPlaying = false;
}

// 初始化弹窗事件
function initializeModal() {
    // 关闭按钮
    closeBtn.addEventListener('click', hideProjectModal);
    
    // 点击遮罩关闭
    modalOverlay.addEventListener('click', function(e) {
        if (e.target === modalOverlay) {
            hideProjectModal();
        }
    });
    
    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modalOverlay.classList.contains('active')) {
            hideProjectModal();
        }
    });
    
    // 阻止弹窗内容区域的点击事件冒泡
    projectModal.addEventListener('click', function(e) {
        e.stopPropagation();
    });
}

// 添加触摸支持
function addTouchSupport() {
    // 防止双击缩放
    document.addEventListener('touchstart', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    });
    
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(e) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            e.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
    
    // 添加触摸反馈
    const touchElements = document.querySelectorAll('.marker, .close-btn');
    touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.style.opacity = '0.7';
        });
        
        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.opacity = '1';
            }, 100);
        });
    });
}

// 页面加载完成后的初始化动画
window.addEventListener('load', function() {
    // 等待地图图片加载完成
    const mapImage = document.querySelector('.beijing-map-image');

    // 初始隐藏所有标记点
    const markers = document.querySelectorAll('.marker');
    markers.forEach(marker => {
        marker.style.opacity = '0';
        marker.style.transform = 'translate(-50%, -50%) scale(0)';
        marker.style.visibility = 'hidden';
    });

    // 检查图片是否已加载
    function startAnimations() {
        // 标记点依次出现动画
        markers.forEach((marker, index) => {
            setTimeout(() => {
                marker.style.visibility = 'visible';
                marker.style.transition = 'all 0.5s ease';
                marker.style.opacity = '1';
                marker.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 300 + index * 100); // 减少间隔时间，让动画更流畅
        });

        // 项目列表动画
        const projectList = document.querySelector('.project-list');
        if (projectList) {
            projectList.style.opacity = '0';
            projectList.style.transform = 'translateY(20px)';

            setTimeout(() => {
                projectList.style.transition = 'all 0.5s ease';
                projectList.style.opacity = '1';
                projectList.style.transform = 'translateY(0)';
            }, 800);
        }
    }

    if (mapImage.complete && mapImage.naturalHeight !== 0) {
        // 图片已经加载完成
        startAnimations();
    } else {
        // 等待图片加载完成
        mapImage.addEventListener('load', startAnimations);
        mapImage.addEventListener('error', function() {
            // 即使图片加载失败，也显示标记点
            console.warn('地图图片加载失败，但仍显示标记点');
            startAnimations();
        });
    }
});

// 工具函数：格式化投资金额
function formatInvestment(amount) {
    return amount.replace(/(\d+)亿元/, '<span class="investment-highlight">$1亿元</span>');
}

// 工具函数：获取项目状态颜色
function getStatusColor(status) {
    const colors = {
        'completed': '#52C41A',
        'in-progress': '#1890FF',
        'planning': '#FA8C16'
    };
    return colors[status] || '#666';
}







// 初始化项目列表
function initializeProjectList() {
    const projectItems = document.querySelectorAll('.project-item');

    projectItems.forEach(item => {
        item.addEventListener('click', function() {
            const projectId = this.getAttribute('data-project');
            highlightProjectMarker(projectId);

            // 添加点击反馈
            this.style.backgroundColor = '#f1f5f9';
            setTimeout(() => {
                this.style.backgroundColor = '';
            }, 200);
        });
    });
}

// 突出显示地图上的项目标记点
function highlightProjectMarker(projectId) {
    const allMarkers = document.querySelectorAll('.marker');
    const targetMarker = document.querySelector(`.marker[data-project="${projectId}"]`);

    if (!targetMarker) return;

    // 重置所有标记点
    allMarkers.forEach(marker => {
        marker.classList.remove('highlighted');
        marker.style.transform = 'translate(-50%, -50%) scale(1)';
        marker.style.zIndex = '10';
    });

    // 突出显示目标标记点
    targetMarker.classList.add('highlighted');
    targetMarker.style.transform = 'translate(-50%, -50%) scale(1.5)';
    targetMarker.style.zIndex = '20';

    // 添加闪烁效果
    let flashCount = 0;
    const flashInterval = setInterval(() => {
        if (flashCount >= 6) {
            clearInterval(flashInterval);
            return;
        }

        targetMarker.style.opacity = flashCount % 2 === 0 ? '0.5' : '1';
        flashCount++;
    }, 300);

    // 3秒后恢复正常
    setTimeout(() => {
        targetMarker.classList.remove('highlighted');
        targetMarker.style.transform = 'translate(-50%, -50%) scale(1)';
        targetMarker.style.zIndex = '10';
        targetMarker.style.opacity = '1';
    }, 3000);
}

// 导出给全局使用
window.ProjectMap = {
    showProject: showProjectModal,
    hideProject: hideProjectModal,
    projectsData: projectsData,
    highlightMarker: highlightProjectMarker
};
