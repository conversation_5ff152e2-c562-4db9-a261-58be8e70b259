# 北京建工集团项目展示平台 - 演示版PRD

## 产品概述
为北京建工集团领导演示打造的移动端项目展示页面，在北京地图上直观展示集团重点项目分布及基本信息。

## 核心功能

### 1. 地图展示
- 使用北京市静态地图图片作为背景
- 地图清晰显示北京主要区域和地标
- 适配移动端屏幕尺寸

### 2. 项目标记
- 在静态地图图片上叠加项目标记点
- 使用醒目的标记图标（如红色圆点或建筑图标）
- 标记位置准确对应实际项目地理位置
- 支持点击交互

### 3. 项目信息展示
点击项目标记后，弹出显示项目信息：
- **项目名称**
- **项目地址**
- **建设规模**
- **项目状态**（规划中/建设中/已完工）
- **投资金额**
- **项目图片**（效果图或实景照片）

## 技术要求

### 实现方案
- **地图方案**：使用北京市静态地图图片
- **技术栈**：HTML + CSS + JavaScript
- **项目标记**：CSS绝对定位 + 点击事件
- **信息展示**：弹窗或侧边栏组件
- **数据存储**：前端JSON数据

### 技术优势
- **快速开发**：无需API申请和配置
- **完全离线**：不依赖网络服务
- **零成本**：无API调用费用
- **演示友好**：加载快速，展示稳定

### 基本要求
- **设备兼容**：支持主流移动设备浏览器
- **加载性能**：页面加载时间不超过2秒
- **交互体验**：点击响应及时，信息展示清晰
- **视觉效果**：界面简洁美观，符合企业形象

## 演示目标
通过静态地图上的项目标记展示，让领导快速了解：
- 集团项目在北京的整体分布情况
- 各项目的基本信息和建设状态
- 集团在北京地区的建设实力和规模

## 实现步骤
1. **准备地图图片**：获取清晰的北京市地图图片
2. **确定项目位置**：标记重点项目的地理坐标
3. **设计标记样式**：制作项目标记图标
4. **开发页面**：HTML+CSS+JS实现交互
5. **测试优化**：移动端适配和性能优化

---
**版本**：演示版 v1.0