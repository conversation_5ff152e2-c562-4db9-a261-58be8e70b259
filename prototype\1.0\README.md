# 北京建工集团项目展示平台 - 高保真原型

## 项目概述
这是一个为北京建工集团领导演示设计的移动端项目地图展示原型，展示集团在北京地区的重点建设项目分布和详细信息。

## 功能特性

### 🗺️ 地图展示
- 使用真实的北京市地图图片 (beijing.png)
- 完全离线，无需依赖第三方地图API
- 响应式设计，适配不同移动设备屏幕
- 高清地图显示，真实地理位置标记

### 📍 项目标记
- 5个示例项目标记，覆盖北京主要区域
- 不同项目状态用不同颜色区分：
  - 🟢 绿色：已完工项目
  - 🔵 蓝色：建设中项目  
  - 🟠 橙色：规划中项目
- 标记点具有脉冲动画效果，增强视觉吸引力

### 💬 项目信息展示
- 点击标记弹出项目详情弹窗
- 包含完整项目信息：名称、地址、规模、状态、投资金额、负责人、联系方式
- 底部滑入式弹窗设计，符合移动端操作习惯

### 📊 统计面板
- 实时显示项目统计信息
- 总项目数、各状态项目数量一目了然

## 技术实现

### 技术栈
- **HTML5**: 语义化标签，良好的可访问性
- **CSS3**: Flexbox布局，CSS动画，响应式设计
- **原生JavaScript**: 无框架依赖，轻量级实现

### 文件结构
```
prototype/1.0/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # 交互脚本
└── README.md          # 说明文档
```

### 设计特点
- **企业色彩**: 使用蓝色系主色调，体现建工集团专业形象
- **移动端优化**: 按iPhone 12 Pro尺寸设计，适配各种移动设备
- **交互友好**: 44px最小触摸区域，流畅的动画效果
- **加载快速**: 纯静态资源，2秒内完成加载

## 示例项目数据

| 项目名称 | 位置 | 状态 | 投资金额 |
|---------|------|------|----------|
| 北京建工大厦 | 朝阳区 | 已完工 | 15亿元 |
| 通州副中心住宅项目 | 通州区 | 建设中 | 25亿元 |
| 大兴机场配套设施 | 大兴区 | 规划中 | 8亿元 |
| 海淀科技园区 | 海淀区 | 建设中 | 12亿元 |
| 丰台商业综合体 | 丰台区 | 已完工 | 18亿元 |

## 使用方法

### 本地预览
1. 确保 `images/beijing.png` 地图文件存在
2. 使用现代浏览器打开 `index.html`
3. 建议使用浏览器的移动设备模拟器查看效果
4. 地图将显示真实的北京市地理位置

### 移动端测试
1. 将文件部署到Web服务器
2. 使用手机浏览器访问
3. 支持iOS Safari 12+、Android Chrome 80+

### 演示操作
1. **查看项目分布**: 页面加载后可看到5个项目标记点
2. **点击标记**: 点击任意标记查看项目详情
3. **查看统计**: 底部统计面板显示项目概况
4. **关闭弹窗**: 点击X按钮或遮罩区域关闭详情弹窗

## 浏览器兼容性

### 移动端
- ✅ iOS Safari 12+
- ✅ Android Chrome 80+
- ✅ 微信内置浏览器
- ✅ QQ浏览器

### 桌面端
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 性能优化

### 加载性能
- 无外部依赖，所有资源本地化
- CSS和JS代码经过优化
- SVG地图矢量图形，文件小巧

### 交互性能
- 使用CSS3硬件加速动画
- 事件委托减少内存占用
- 防抖处理避免重复触发

## 扩展建议

### 数据扩展
- 可将项目数据改为从JSON文件或API加载
- 支持更多项目信息字段
- 添加项目图片展示功能

### 功能扩展
- 添加项目搜索功能
- 支持项目筛选（按状态、区域等）
- 添加项目收藏功能
- 支持分享项目信息

### 视觉优化
- 使用真实的北京地图图片
- 添加更丰富的项目图标
- 支持深色模式
- 添加更多动画效果

## 联系信息
如需技术支持或功能定制，请联系开发团队。

---
**版本**: v1.0  
**更新时间**: 2025年1月  
**兼容性**: 移动端优先，响应式设计
