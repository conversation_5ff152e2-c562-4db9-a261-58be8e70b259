# 北京建工集团项目展示平台 - 原型设计提示词

## 项目背景
基于《北京建工集团项目展示平台 - 演示版PRD》，为领导演示设计移动端项目地图展示页面原型。

## 设计要求

### 1. 整体风格
- **企业形象**：体现北京建工集团的专业性和权威性
- **色彩搭配**：主色调使用蓝色系（#1890FF）和灰色系（#666666），体现建筑行业的稳重感
- **设计风格**：简洁现代，符合移动端操作习惯
- **品牌元素**：适当融入建工集团LOGO和企业色彩

### 2. 页面布局
- **移动端优先**：按iPhone 12 Pro（390x844px）尺寸设计
- **响应式设计**：适配不同移动设备屏幕
- **导航简洁**：顶部显示页面标题"北京建工集团项目分布"
- **内容区域**：地图占据主要视觉区域

### 3. 地图区域设计
- **地图背景**：使用清晰的北京市地图图片，包含主要区域和地标
- **地图尺寸**：占屏幕高度的70-80%，保证项目标记清晰可见
- **边框样式**：地图区域添加subtle阴影，增强层次感

### 4. 项目标记设计
- **标记图标**：使用醒目的红色圆点（#FF4D4F）或建筑图标
- **标记大小**：直径20-24px，确保手指点击友好
- **标记状态**：
  - 规划中：橙色标记（#FA8C16）
  - 建设中：蓝色标记（#1890FF）
  - 已完工：绿色标记（#52C41A）
- **标记动效**：hover时轻微放大（scale: 1.1）

### 5. 项目信息弹窗设计
- **弹窗样式**：从底部滑入的卡片式弹窗
- **弹窗内容**：
  - 项目名称（18px，加粗）
  - 项目地址（14px，灰色）
  - 建设规模（14px）
  - 项目状态（带状态色彩的标签）
  - 投资金额（16px，突出显示）
  - 项目图片（占位图或示例图片）
- **关闭按钮**：右上角X按钮，易于操作
- **背景遮罩**：半透明黑色遮罩（rgba(0,0,0,0.5)）

### 6. 交互设计
- **点击反馈**：标记点击时有视觉反馈（颜色变化或动画）
- **弹窗动画**：smooth的滑入滑出动画（300ms）
- **触摸优化**：所有可点击元素符合44px最小触摸区域

### 7. 示例数据
请在原型中包含3-5个示例项目：
- **项目1**：北京建工大厦（朝阳区，已完工，投资15亿元）
- **项目2**：通州副中心住宅项目（通州区，建设中，投资25亿元）
- **项目3**：大兴机场配套设施（大兴区，规划中，投资8亿元）
- **项目4**：海淀科技园区（海淀区，建设中，投资12亿元）
- **项目5**：丰台商业综合体（丰台区，已完工，投资18亿元）

## 技术实现要求

### 1. 代码规范
- 使用HTML5 + CSS3 + 原生JavaScript
- 代码结构清晰，注释完整
- CSS使用Flexbox布局
- 遵循移动端最佳实践

### 2. 文件结构
- **主页面**：index.html
- **样式文件**：styles.css
- **脚本文件**：script.js
- **图片资源**：images/文件夹
- **输出路径**：prototype/1.0/

### 3. 性能优化
- 图片压缩优化
- CSS和JS代码压缩
- 页面加载时间控制在2秒内

### 4. 兼容性
- 支持iOS Safari 12+
- 支持Android Chrome 80+
- 支持微信内置浏览器

## 参考竞品
- 高德地图的POI展示方式
- 百度地图的信息弹窗设计
- 企业官网的项目展示页面
- 房地产项目的地图选房功能

## 交付物
1. **高保真HTML原型**：完整可交互的演示页面
2. **设计说明文档**：关键设计决策的说明
3. **资源文件**：所需的图片、图标等素材
4. **演示数据**：JSON格式的示例项目数据

## 验收标准
- ✅ 页面在移动端显示正常，无布局错乱
- ✅ 项目标记点击后能正确显示项目信息
- ✅ 弹窗动画流畅，用户体验良好
- ✅ 整体视觉效果专业，符合企业形象
- ✅ 代码结构清晰，易于维护和扩展